import request from "@/request";

export interface ApiResponse<T> {
  code: number;
  result: string;
  data: T;
}

// 我的媒体库中的lib对象
export interface MyLibProps {
  lib_id: number;
  name: string;
  scan_path: string[];
  tv_visable: number;
  create_time: string;
  update_time: string;
  scan_percent: number;
  scan_status: string; // 未开始、扫描中、扫描完成、扫描异常
  scan_error_code: string; // 无错误、系统重启中断、未知错误
  share_to_uid_count?: number;
  share_to_uid?: number[];
  share2who_count?: number;
  share2who_list?: (string | number)[];
  poster: string[];
  media_count: number // 媒体数量
}

// 最近观看的影视文件信息
export interface RecentlyPlayedFileInfo {
  audio_codec: string;
  file_id: number;
  favourite: number;
  episode: number;
  media_name: string;
  duration: number;
  last_play_point: number;
  resolution: string;
  media_classes: string;
  last_seen_time: number;
  mtime: number;
  hdr: string;
  seen: number;
  file_size: number;
  session: number;
  path: string;
  poster: string[];
  last_seen_percent: number; // 上次观看文件的已观看进度
  media_id: number;
  brief: string // 简介
}

// 最近观看的影视文件列表返回数据
export interface RecentlyPlayedResponse {
  count: number;
  files: RecentlyPlayedFileInfo[];
}

// 分享的媒体库lib对象
export interface SharedLibProps {
  lib_id: number;
  name: string;
  share_from: number;
  tv_visable: number;
  create_time: string;
  update_time: string;
  scan_percent: number;
  scan_status: string; // 未开始、扫描中、扫描完成、扫描异常
  scan_error_code: string; // 无错误、系统重启中断、未知错误
  poster: string[];
  media_count: number // 媒体数量
}

// 媒体库对象
export interface libProps {
  lib_id: string;
  name: string;
  scan_path: string[];
  tv_visable: number;
  create_time: string;
  scan_percent: number;
  share_to_uid_count: number;
  share_to_uid: number[];
}

// 我的媒体库详情
export interface MyLibraryProps {
  count: number;
  libs: MyLibProps[];
}

// 分享给我的媒体库详情
export interface SharedLibraryProps {
  count: number;
  libs: SharedLibProps[];
}

// 媒体库详情
export interface libraryProps {
  count: number;
  libs: libProps[];
}

// 媒体库数据结构
export interface LibraryDataProps {
  my_libs: MyLibraryProps;
  share2me: SharedLibraryProps;
}

// 媒体库列表
export interface libraryListProps {
  [key: string]: libraryProps
}

// 影视列表中媒体对象
export interface mediaProps {
  media_id: number;
  classes: string;
  score?: number;
  midb_id: string;
  tmdb_score: number;
  origin_place: string;
  origin_region: string;
  kind: string[];
  year: number;
  language: string;
  favourite: number;
  seen: number;
  last_seen_percent: number; // 上次观看文件的已观看进度
  last_seen_time: number;
  video_length: number;
  trans_name: string;
  origin_name: string;
  other_name: string;
  actor_names: string[];
  director_names: string[];
  screenwriter_names: string[];
  poster: string[];
  create_time: number;
  index?: number
  brief: string // 简介
}

// 影视列表
export interface filmAndTvProps {
  count: number;
  medias: mediaProps[];
}

export interface filterBasicParams {
  offset: number;
  limit: number;
}

export interface mediaFilters {
  resolution?: string,
  hdr?: string,
  region?: string,
  classes?: string,
  kind?: string[],
  year_start?: number,
  year_end?: number,
}

export interface mediaListParams {
  lib_id: number;
  filter: filterBasicParams & mediaFilters & {
    favourite?: number;
    seen?: number;
    sort_type?: number;
    asc?: number;
  }
}

// 创建媒体库的入参
export interface CreateLibParams {
  name: string;
  tv_visable: number;
  scan_path: string[];
  share2who?: (string | number)[];
}

// 创建媒体库的返回数据
export interface CreateLibResponse {
  lib_id: number;
}

// 删除媒体库的入参
export interface DeleteLibParams {
  lib_id: number;
}

// 扫描媒体库的入参
export interface ScanLibParams {
  lib_id: number;
}

// 编辑媒体库的入参
export interface EditLibParams {
  lib_id: number;
  name?: string;
  tv_visable?: number;
  path_del?: string[];
  path_add?: string[];
  share_del?: (string | number)[];
  share_add?: (string | number)[];
}

// 退出媒体库分享的入参
export interface ExitShareParams {
  lib_id: number;
}

// 获取影视详情的入参
export interface MediaDetailsParams {
  media_id: number;
}

// 获取影视文件列表的入参
export interface MediaFileParams {
  lib_id: number;
  media_id: number;
}

// 影视文件信息
export interface MediaFileInfo {
  subtitle_index: any;
  subtitle_type: any;
  subtitle_path: any;
  audio_index: any;
  file_id: number;
  path: string;
  resolution: string;
  hdr: string;
  audio_codec: string;
  session: number;
  episode: number;
  last_play_point: number;
  file_size: number;
  mtime: string;
  duration: number;
  media_id: number;
  favourite: number; // 收藏状态：0未收藏，1已收藏
  seen: number; // 观看状态：0未观看，1已观看
}

// 影视文件列表返回数据
export interface MediaFileResponse {
  count: number;
  files: MediaFileInfo[];
}

// 演员/人员信息
export interface PersonInfo {
  name: string;
  original_name: string;
  profile_path: string;
  chapter?: string; // 角色名称，演员才有
}

// 影视详情数据
export interface MediaDetailsResponse {
  media_id: number;
  classes: string;
  score?: number;
  origin_place: string;
  kind: string[];
  year: number;
  language: string;
  favourite: number;
  seen: number;
  last_seen_file_id: number;
  last_seen_time: number;
  video_length: number;
  trans_name: string;
  origin_name: string;
  other_name: string;
  brief: string;
  actor_info: PersonInfo[];
  director_info: PersonInfo[];
  screenwriter_info: PersonInfo[];
  season_info?: number;
  last_play_point?: number;
  poster: string[];
}

// 获取池信息入参
export interface poolInfoParams {
  page: object;
  order: object;
  path: object;
}

// 存储池类型定义
export interface StoragePool {
  name: string;
  pool_type: string;
  data_dir: string;
  photo_dir: string;
  total_size: string;
  used_size: string;
}

// WebDAV配置类型定义
export interface WebDAVConfig {
  username: string;
  password: string;
  uri: string;
  alias_root: string;
  port: number;
}

// 外部存储池类型定义
export interface ExternalPool {
  name: string;
  data_dir: string;
  total_size: string;
  used_size: string;
}

// 存储池信息返回数据
export interface PoolInfoResponse {
  webDAV: WebDAVConfig;
  internal_pool: StoragePool[];
  external_pool: ExternalPool[];
}

// 文件/文件夹项目定义
export interface FileItem {
  name: string;
  size: string;
  parent: string;
  modified_time: string;
  xattr: {
    directory?: boolean;
    favorite?: boolean;
    media_duration?: number;
  };
}

// 目录列表入参
export interface ListDirectoryParams {
  path: {
    parent: string;
    recursion: boolean;
  };
  page?: {
    size: number;
    token: string;
  }
}

// 目录列表返回数据
export interface DirectoryListResponse {
  page: {
    size: number;
    token: string;
  };
  files: FileItem[];
}

// 用户信息类型定义
export interface UserInfo {
  uid: string;
  permission: 'admin' | 'user';
  nickname: string;
  group: string[];
  status: 'active' | 'inactive';
  create_time: string;
  delete_time: string;
  total_size: number;
  used_size: number;
  source: 'init' | 'share' | 'family';
}

// 用户列表查询参数
export interface UserListParams {
  selector: {
    key: 'status';
    value: string[];
  };
}

// 用户列表返回数据
export interface UserListResponse {
  list: UserInfo[];
}

// 获取最近播放影视列表
export const getRecentlyWatched = (
  filter: filterBasicParams,
  config?: { loadingMode?: 'full' | 'icon' }
): Promise<ApiResponse<RecentlyPlayedResponse>> => {
  const requestConfig = config?.loadingMode
    ? { loadingMode: config.loadingMode }
    : { showLoading: false };
  return request.post('/mediacenter/media_recently_watched', { filter }, requestConfig);
};

// 获取最近添加影视列表
export const getRecentlyAdd = (
  filter: filterBasicParams,
  config?: { loadingMode?: 'full' | 'icon' }
): Promise<ApiResponse<filmAndTvProps>> => {
  const requestConfig = config?.loadingMode
    ? { loadingMode: config.loadingMode }
    : { showLoading: false };
  return request.post('/mediacenter/media_recently_added', { filter }, requestConfig);
};

// 搜索在线影视
export const searchOnlineMedia = (
  keyword: string,
  config?: { loadingMode?: 'full' | 'icon' }
): Promise<ApiResponse<mediaProps>> => {
  const requestConfig = config?.loadingMode
    ? { loadingMode: config.loadingMode }
    : { showLoading: false };
  return request.post('/mediacenter/media_online', { keyword }, requestConfig);
};

// 影视项修正匹配信息
export const rematchMedia = (
  params: {
    media_ids: number[];
    lib_id: number;
    keyword: string;
    index: number;
  }
): Promise<ApiResponse<{
  new_media_id?: number
}>> => {
  return request.post('/mediacenter/media_rematch', params);
}

// 获取完整媒体库数据（包含我的库和分享给我的库）
export const getFullLibraryData = (
  filter: {
    sort_type?: number;
    asc?: number;
  },
  config?: { loadingMode?: 'full' | 'icon' }
): Promise<ApiResponse<LibraryDataProps>> => {
  const requestConfig = config?.loadingMode
    ? { loadingMode: config.loadingMode }
    : { showLoading: false };
  return request.post('/mediacenter/lib_list', { filter }, requestConfig);
};

// 创建媒体库
export const createLibrary = (
  params: CreateLibParams
): Promise<ApiResponse<CreateLibResponse>> => {
  return request.post('/mediacenter/lib_create', params);
};

// 删除媒体库
export const deleteLibrary = (
  params: DeleteLibParams
): Promise<ApiResponse<{}>> => {
  return request.post('/mediacenter/lib_delete', params);
};

// 扫描媒体库
export const scanLibrary = (
  params: ScanLibParams
): Promise<ApiResponse<{}>> => {
  return request.post('/mediacenter/lib_scan', params);
};

// 编辑媒体库
export const editLibrary = (
  params: EditLibParams
): Promise<ApiResponse<{}>> => {
  return request.post('/mediacenter/lib_edit', params);
};

// 退出媒体库分享
export const exitLibraryShare = (
  params: ExitShareParams
): Promise<ApiResponse<{}>> => {
  return request.post('/mediacenter/lib_exit_share', params);
};

// 获取影视详情
export const getMediaDetails = (
  params: MediaDetailsParams,
  config?: { loadingMode?: 'full' | 'icon' }
): Promise<ApiResponse<MediaDetailsResponse>> => {
  const requestConfig = config?.loadingMode
    ? { loadingMode: config.loadingMode }
    : { showLoading: false };
  return request.post('/mediacenter/media_details', params, requestConfig);
};

// 获取影视文件列表
export const getMediaFiles = (
  params: MediaFileParams,
  config?: { loadingMode?: 'full' | 'icon' }
): Promise<ApiResponse<MediaFileResponse>> => {
  const requestConfig = config?.loadingMode
    ? { loadingMode: config.loadingMode }
    : { showLoading: false };
  return request.post('/mediacenter/file_list', params, requestConfig);
};

// 搜索本地影视列表
export const searchLocalMedia = (
  params: {
    keyword: string;
    filter: filterBasicParams & {
      classes?: string; // tv表示电视剧，movie表示电影，空字符串表示全部
    }
  },
  config?: { loadingMode?: 'full' | 'icon' }
): Promise<ApiResponse<filmAndTvProps>> => {
  const requestConfig = config?.loadingMode
    ? { loadingMode: config.loadingMode }
    : { showLoading: false };
  return request.post('/mediacenter/media_search', params, requestConfig);
};


// 获取存储池信息
export const getPoolInfo = ({ }): Promise<ApiResponse<PoolInfoResponse>> => {
  return request.post('/filemgr/get_pool_info', {});
};

// 获取目录列表
export const listDirectory = (
  params: ListDirectoryParams
): Promise<ApiResponse<DirectoryListResponse>> => {
  return request.post('/filemgr/list_directory', params);
};

// 获取用户列表
export const getUserList = (
  params: UserListParams
): Promise<ApiResponse<UserListResponse>> => {
  return request.post('/usermanager/user_list', params);
};


// 影视标记已观看 0:未观看 1:已观看
export const markWatched = (
  params: {
    media_ids: number[];
    seen: number;
  }
): Promise<ApiResponse<{}>> => {
  return request.post('/mediacenter/media_seen', params);
}

// 影视收藏 0:取消收藏 1:收藏
export const collect = (
  params: {
    media_ids: number[];
    favourite: number;
  }
): Promise<ApiResponse<{}>> => {
  return request.post('/mediacenter/media_favourite', params);
}

// 影视单集收藏 0:取消收藏 1:收藏
export const collectEpisode = (
  params: {
    file_id: number;
    favourite: number;
  }
): Promise<ApiResponse<{}>> => {
  return request.post('/mediacenter/file_favourite', params);
}

// 影视单集标记已观看 0:未观看 1:已观看
export const markWatchedEpisode = (
  params: {
    file_id: number;
    seen: number;
  }
): Promise<ApiResponse<{}>> => {
  return request.post('/mediacenter/file_seen', params);
}

// 获取媒体库影视列表
export const getMediaListFromLib = (params: mediaListParams, config?: { loadingMode?: 'full' | 'icon' }): Promise<ApiResponse<filmAndTvProps>> => {
  const requestConfig = config?.loadingMode
    ? { loadingMode: config.loadingMode }
    : { showLoading: false };
  return request.post('/mediacenter/media_list', params, requestConfig);
}

//影视文件修正匹配信息
export const fileRematch = (
  params: {
    file_id: number;
    keyword: string;
    index: number;
  }
): Promise<ApiResponse<{}>> => {
  return request.post('/mediacenter/file_rematch', params);
}

// 影视文件删除影视
export const fileDelete = (
  params: {
    file_id: number;
  }
): Promise<ApiResponse<{}>> => {
  return request.post('/mediacenter/file_delete', params);
}

// 影视项删除影视
export const mediaDelete = (
  params: {
    media_ids: number[];
    lib_id: number
  }
): Promise<ApiResponse<{}>> => {
  return request.post('/mediacenter/media_delete', params);
}

// 回收站任务返回
export interface Move2TrashbinResponse {
  task_id: string;
}

// 移动文件/文件夹到回收站
export const move2trashbin = (
  params: { path: string[] }
): Promise<ApiResponse<Move2TrashbinResponse>> => {
  return request.post('/filemgr/move2trashbin', params);
};

// 删除最近观看记录
export const mediaClearWatched = (
  params: { file_ids: number[] }
): Promise<ApiResponse<{}>> => {
  return request.post('/mediacenter/media_clear_watched', params);
};

// 获取影视文件
export const getMediaFile = (
  params: {
    media_id: number;
    lib_id: number;
  }
): Promise<ApiResponse<{}>> => {
  return request.post('/mediacenter/media_file', params);
};

// 获取影视文件路径列表
export const getFilePath = (params: { lib_id: number, media_ids: number[] }): Promise<ApiResponse<{ path: string[] }>> => request.post('/mediacenter/file_path_list', params);

type thumbnailType = 'small' | 'medium' | 'big' | 'preview';
export const get_thumbnail = (params: { path: string, size: thumbnailType }): Promise<ApiResponse<{}>> => request.post('/filemgr/get_thumbnail');
// 文件管理获取缩略图
export const getThumbnail = (params: { path: string, size: thumbnailType }): Promise<Blob> => request.post('/filemgr/get_thumbnail', params, { responseType: 'arraybuffer', showLoading: false });

// 任务详情选择器参数
export interface TaskInfoSelector {
  key: 'task_id' | 'module' | 'type';
  value: string[];
}

// 任务详情分页参数
export interface TaskInfoPage {
  size: number;
  id: number;
}

// 任务详情请求参数
export interface GetTaskInfoParams {
  selector?: TaskInfoSelector[];
  page?: TaskInfoPage;
}

// 任务详情数据
export interface TaskDetail {
  progress: number;
  total_files: number;
  handled_files: number;
  total_size: string;
  handled_size: string;
  handling_file: string;
  speed: string;
}

// 任务信息
export interface TaskInfo {
  task_id: string;
  src: string[];
  src_type: string;
  dst: string;
  status: 'running' | 'done' | 'failed' | 'pending';
  module: string;
  create_time: string;
  done_time: string;
  action: string;
  detail: TaskDetail;
}

// 任务详情分页返回信息
export interface TaskInfoPageResponse {
  size: number;
  total_page: number;
  total_task: number;
}

// 任务详情返回数据
export interface TaskInfoResponse {
  page: TaskInfoPageResponse;
  info: TaskInfo[];
}

// 获取任务详情
export const getTaskInfo = (
  params: GetTaskInfoParams
): Promise<ApiResponse<TaskInfoResponse>> => {
  return request.post('/taskcenter/get_taskinfo', params);
};

// 获取推荐影视列表
export const getMediaListFromRecommend = (params: { count: number }, config?: { loadingMode?: 'full' | 'icon' }): Promise<ApiResponse<filmAndTvProps>> => {
  const requestConfig = config?.loadingMode
    ? { loadingMode: config.loadingMode }
    : { showLoading: false };
  return request.post('/mediacenter/media_recommend', params, requestConfig);
}

