import all from '@/Resources/player/allEvent.png';
import human from '@/Resources/player/human.png';
import move from '@/Resources/player/move.png';
// import noise from '@/Resources/player/noise.png';
// import { px2rem } from '@/utils/setRootFontSize';
import move_ from '@/Resources/player/move_.png';
// import noise_ from '@/Resources/player/noise_.png';
import pet from '@/Resources/player/pet.png';
import fire from '@/Resources/player/fire.png';
import all_ from '@/Resources/player/all_.png';
import human_ from '@/Resources/player/human_.png';
import pet_ from '@/Resources/player/pet_.png';
import fire_ from '@/Resources/player/fire_.png';


interface IIdentifyEvent {
  name: string
  label: string
  icon: string
  valueIcon: string
  color: string
}

// export type EventLookBackType = 'all' | 'move' | 'human' | 'fire' | 'pet' | 'noise';
export type EventLookBackType = 'all' | 'human' | 'fire' | 'pet' | 'move';
export enum EventType {
  human = 'human',
  fire = 'fire',
  pet = 'pet',
  move = 'move'
}

export const event_color_all = "rgba(0, 113, 205, 1)";
export const event_color_move = "rgba(253, 197, 65, 1)";
export const event_color_human = "rgba(68, 206, 202, 1)";
export const event_color_fire = "rgba(110, 130, 253, 1)";
export const event_color_pet = "rgba(117, 223, 110, 1)";
export const event_color_noise = "rgba(125, 166, 224, 1)";


export const eventDefinition: { [key: string]: IIdentifyEvent } = {
  all: { name: 'all', label: '全部事件', icon: all, valueIcon: all_, color: event_color_all },
  human: { name: 'human', label: '人形检测', icon: human, valueIcon: human_, color: event_color_human },
  move: { name: 'move', label: '移动检测', icon: move, valueIcon: move_, color: event_color_move },
  fire: { name: 'fire', label: '烟火检测', icon: fire, valueIcon: fire_, color: event_color_fire },
  pet: { name: 'pet', label: '宠物检测', icon: pet, valueIcon: pet_, color: event_color_pet },
  // noise: { name: 'noise', label: '异响侦测', icon: noise, valueIcon: noise_, color: event_color_noise },
}

export const eventTypeList = [
  { type: 'move', label: '移动检测', describe: 'AI检测画面中的变化，不同灵敏度将产生不同数量记录', icon: move },
  { type: 'human', label: '人形检测', describe: 'AI检测画面中是否有人出现，出现后记录为事件', icon: human },
  { type: 'fire', label: '烟火检测', describe: 'AI检测画面中是否有明火出现，帮你及时发现家庭火情风险', icon: fire },
  { type: 'pet', label: '宠物检测', describe: 'AI检测画面中的是否有宠物出现，帮你及时了解萌宠状态', icon: pet },
  // { type: 'noise', label: '异响检测', describe: 'AI检测周围环境是否有较大声音，让你知晓家庭异常', icon: noise },
]

export const eventSelectorOptions = [
  { label: "全部事件", value: "all", icon: eventDefinition['all'].icon },
  { label: "人形检测", value: "human", icon: eventDefinition['human'].icon },
  { label: '烟火检测', value: "fire", icon: eventDefinition['fire'].icon },
  { label: "宠物检测", value: "pet", icon: eventDefinition['pet'].icon },
  { label: "移动检测", value: "move", icon: eventDefinition['move'].icon },
  // { label: "异响侦测", value: "异响侦测", icon: eventDefinition['noise'].icon },
]

export const SIDE_BAR_WIDTH = `${((260 / 1234) * 100).toFixed(2)}%`

type cameraName = '小米智能摄像机3 Pro 云台版' | '小米室外摄像机BW500' | '小米智能摄像机3 云台版' | '小米室外摄像机CW500双摄版' | '小米智能摄像机 母婴看护版' | '小米智能摄像机 视频通话版'

export const cameraIconInfo = (cameraModel: cameraName | string) => {
  switch (cameraModel) {
    case 'chuangmi.camera.061a01': return require('@/Resources/camMgmtImg/cameraIcon1.png');
    case 'chuangmi.camera.069a01': return require('@/Resources/camMgmtImg/cameraIcon2.png');
    case 'chuangmi.camera.039c01': return require('@/Resources/camMgmtImg/cameraIcon3.png');
    case 'chuangmi.camera.ip029a': return require('@/Resources/camMgmtImg/cameraIcon4.png');
    case 'chuangmi.camera.v2': return require('@/Resources/camMgmtImg/cameraIcon5.png');
    case 'mxiang.camera.moc001': return require('@/Resources/camMgmtImg/cameraIcon6.png');
    case 'chuangmi.camera.72ac1': return require('@/Resources/camMgmtImg/cameraIcon7.png');
    case 'isa.camera.hlmax': return require('@/Resources/camMgmtImg/cameraIcon8.png');
    case 'isa.camera.cw500': return require('@/Resources/camMgmtImg/cameraIcon9.png');
    case 'mxiang.camera.c500ch': return require('@/Resources/camMgmtImg/cameraIcon10.png');
    case 'chuangmi.camera.81ac1': return require('@/Resources/camMgmtImg/cameraIcon11.png');
    case 'isa.camera.hlzoom': return require('@/Resources/camMgmtImg/cameraIcon12.png');
    default: return require('@/Resources/camMgmtImg/cameraIcon3.png');
  }
}