import React from "react";
import ReactD<PERSON> from "react-dom";
import { <PERSON>h<PERSON><PERSON><PERSON> as Router } from "react-router-dom";
import "@/index.css";
import "@/assets/index.css";
import App from "./App";
import ThemeDetector from "./utils/themeDetector";

ReactDOM.render(
  <React.StrictMode>
    <Router>
      <ThemeDetector>
        <App />
      </ThemeDetector>
    </Router>
  </React.StrictMode>,
  document.getElementById('root')
);
