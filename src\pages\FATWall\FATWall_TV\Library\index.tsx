import React, { useCallback, useEffect, useMemo, useRef, useState } from "react"
import styles from '../optPages/index.module.scss';
import FilmFilter from "@/components/FATWall_TV/FilmFilter";
import { defaultFiltersByTV } from "@/components/FATWall_APP/FATWALL_CONST";
import FilmCard, { IFilmCard } from "@/components/FATWall_PC/FilmCard";
import { useInViewport, useRequest, useUpdateEffect } from 'ahooks';
import { getMediaListFromLib, mediaProps } from "@/api/fatWall";
import { defaultPageParam } from "../../FATWall_APP/Recently";
import ErrorComponentTV from "../Error";
import { useLibraryListTV } from "..";
import { handleFilterByTV } from "../optPages/All";
import { useParams, useHistory } from "react-router-dom";

export interface IFilter {
  name: string;
  typeList: {
    label: string;
    value: string;
  }[];
}

const Library = () => {
  const [filters, setFilters] = useState<{ [key: string]: string }>(defaultFiltersByTV);
  const history = useHistory();

  // 获取影视列表数据
  const [medias, setMedias] = useState<(mediaProps)[]>([]);
  const { runAsync } = useRequest(getMediaListFromLib, { manual: true });
  const pageOptRef = useRef<{ offset: number, limit: number }>(defaultPageParam); // 分页参数
  const prevFilters = useRef<{ [key: string]: string }>(defaultFiltersByTV); // 记录之前的筛选条件，用于判断筛选条件是否变化

  // 加载更多的必要参数
  const loaderRef = useRef<HTMLDivElement>(null);
  const [hasMore, setHasMore] = useState(true);

  const [isError, setIsError] = useState<boolean>(false); // 查询是否出错，用于显示刷新按钮
  const libs = useLibraryListTV();

  const { id } = useParams<{ id: string }>();
  const idRef = useRef<number>(0);

  const fetchMedias = useCallback(async (callback: (data: mediaProps[]) => void, filter?) => {
    const res = await runAsync({ lib_id: idRef.current, filter: { ...pageOptRef.current, ...filter } }, { loadingMode: 'icon' }).catch((e) => console.log('获取媒体库影视列表失败：', e));
    if (res && res.code === 0 && res.data) {
      if (res.data.count < pageOptRef.current.limit) setHasMore(false);
      callback(res.data.medias); // 根据是筛选还是改变页数更新状态
      setIsError(false);
      return;
    }
    setIsError(true);
  }, [runAsync])

  const filmList: IFilmCard[] = useMemo(() => {
    return medias.map((item) => {
      return {
        title: item.trans_name,
        url: item.poster.length > 0 ? item.poster[0] : '',
        name: item.trans_name || item.origin_name || item.other_name,
        score: item.score ? item.score.toFixed(1).toString() : '暂无评分',
        isDrama: false,
        favourite: item.favourite === 1,
      }
    })
  }, [medias]);

  // 滚动到底部加载更多数据
  const [inViewport] = useInViewport(loaderRef, {
    threshold: 0.2,
  })

  useUpdateEffect(() => {
    if (inViewport) {
      pageOptRef.current = { ...pageOptRef.current, offset: pageOptRef.current.offset + pageOptRef.current.limit };
      fetchMedias((data) => setMedias(p => [...p, ...data]), handleFilterByTV(filters));
    }
  }, [inViewport, fetchMedias])

  const getDataByFilters = useCallback((filters) => {
    const filter = handleFilterByTV(filters); // 处理筛选条件
    // 筛选条件更新的时候，重置页数，重置是否还有更多数据
    setHasMore(true);
    pageOptRef.current = defaultPageParam; // 重置页数
    fetchMedias((data) => setMedias(data), filter);
  }, [fetchMedias])

  useUpdateEffect(() => {
    prevFilters.current = { ...filters }; // 记录之前的筛选条件，用于判断筛选条件是否变化
  }, [filters])

  useUpdateEffect(() => {
    getDataByFilters(filters);
  }, [filters, getDataByFilters])

  // 筛选条件重置和刷新按钮逻辑
  const clearAndRefresh = useCallback(() => {
    setFilters(defaultFiltersByTV); // 重置筛选条件,同时会重置页数

    // 如果页面参数没有变化则手动加载数据
    if (prevFilters.current) {
      if (JSON.stringify(prevFilters.current) === JSON.stringify(defaultFiltersByTV)) {
        getDataByFilters(defaultFiltersByTV);
      }
    }
  }, [getDataByFilters])

  // 跳转到VideoDetails页面
  const toVideoDetails = useCallback((item: mediaProps) => {
    const params = new URLSearchParams({
      classes: item.classes || '',
      media_id: item.media_id?.toString() || '0',
      lib_id: '0'
    });

    history.push(`/filmAndTelevisionWall_tv/videoDetails?${params.toString()}`);
  }, [history]);

  useEffect(() => {
    // 每次 id 变化时，重置页面相关 state，初始化的时候也会因为filters变化而触发，所以放在id变化时重置所有状态
    if (id) {
      idRef.current = Number(id);
      setMedias([]);
      setHasMore(true);
      setIsError(false);

      clearAndRefresh();
    }
  }, [clearAndRefresh, id]);

  return (
    <div className={styles["nasTV_optPages_container"]}>
      <FilmFilter setFilters={setFilters} filters={filters} isLibrary={true} />
      <ErrorComponentTV customizeRow={10} isError={isError} hasContent={filmList.length !== 0} hasLibrary={libs.libs.length !== 0} refresh={clearAndRefresh}
        text={isError ? '获取失败' : libs.libs.length === 0 ? '暂无媒体库' : '暂无内容'} subText={libs.libs.length === 0 ? '请在手机或电脑应用创建' : '请在手机或电脑应用中，向对应媒体库文件夹添加视频文件'}>
        <div className={styles.nasTV_optPages_content}>
          {
            filmList.map((item, index) => (
              <FilmCard
                key={item.name}
                {...item}
                row={Math.floor(index / 5) + Object.keys(filters).length}
                col={index % 5}
                id={`tv-id-library-${item.name}`}
                onClick={() => toVideoDetails(medias[index])}
              />
            ))
          }

          {
            hasMore && filmList.length >= pageOptRef.current.limit && (<div ref={loaderRef} style={{ padding: '20px', height: '20px' }}></div>)
          }
        </div>
      </ErrorComponentTV>
    </div>
  );
}

export default Library;
