import { useState, useEffect, useRef } from "react";
import { List, Toast } from "antd-mobile";
import {
  useHistory,
  useRouteMatch,
  Route,
  useLocation,
} from "react-router-dom";
import { StorageContext } from "./Context/storageContext";
import styles from "./index.module.scss";
import arrowLeft from "@/Resources/camMgmtImg/arrow-left.png";
import arrowLeftDark from "@/Resources/camMgmtImg/arrow-left-dark.png";
import NavigatorBar from "@/components/NavBar";
import { useTheme } from "@/utils/themeDetector";
import { CameraInfo, listRecordCamera, getSupportCameraList } from "@/api/ipc";
import { useRequest } from "ahooks";
import { PreloadImage } from "@/components/Image";
import { cameraIconInfo } from "@/components/CameraPlayer/constants";

// 摄像机信息类型定义
interface ICameraInfoList {
  model_name: string;
  model: string;
  icon: string;
}

const StorageManagement = (props: { children?: React.ReactNode }) => {
  const history = useHistory();
  const location = useLocation<{ cameraList?: CameraInfo[] }>();
  const { path } = useRouteMatch();
  const { isDarkMode } = useTheme();

  const [cameras, setCameras] = useState<CameraInfo[]>([]);
  const isInitializedRef = useRef(false);

  // 摄像机机型信息
  const [cameraInfoList, setCameraInfoList] = useState<ICameraInfoList[]>([]);
  // 支持摄像机列表是否已加载
  const [isCameraInfoLoaded, setIsCameraInfoLoaded] = useState<boolean>(false);

  // 获取摄像机图标的函数
  const getCameraIcon = (model: string): string => {
    const item = cameraInfoList.find((it) => it.model === model);
    return item ? item.icon : cameraIconInfo(model);
  };

  // 获取支持的摄像机列表
  useRequest(getSupportCameraList, {
    manual: false,
    onError(e) {
      console.log('获取可支持ipc信息失败:', e);
    },
    onSuccess(res: any) {
      if (res.code === 0 && res.data) {
        setCameraInfoList(res.data.camera.map((item: ICameraInfoList) => {
          const origin = window.location.origin;
          const pathname = window.location.pathname;
          const needPath = pathname.split('/').slice(1, 4).join('/'); // APP端路径处理
          return {
            ...item,
            icon: `${origin}/${needPath}/${item.icon}`,
          }
        }));
        setIsCameraInfoLoaded(true);
      }
    }
  });

  const { run: refreshCameraList } = useRequest(() => listRecordCamera({ did: [] }), {
    manual: true,
    onSuccess: (res) => {
      if(res && (res.code === 5000 || res.code === 1700)){
        Toast.show(res?.result);
        return;
      }
      const cameraList = res.data?.camera || [];
      setCameras(cameraList);
    },
    onError: (err) => {
      console.log('err: ', err);
    },
  });

  // 初始化数据和处理返回时的刷新
  useEffect(() => {
    // 当路径变为存储管理主页面时
    if (location.pathname === path) {
      // 优先使用从Home页面传递过来的数据（仅在首次进入时）
      const passedCameraList = location.state?.cameraList;
      if (passedCameraList && passedCameraList.length > 0 && !isInitializedRef.current) {
        // 首次进入且有传递数据时使用传递的数据
        setCameras(passedCameraList);
        isInitializedRef.current = true;
      } else {
        // 从子页面返回或没有传递数据时调用接口刷新
        refreshCameraList();
      }
    }
  }, [location.pathname, path, location.state?.cameraList, refreshCameraList]);

  // 初始化阈值和检测事件状态
  const [threshold, setThreshold] = useState(90);
  const [detectEvents, setDetectEvents] = useState({
    motionDetect: true,
    humanDetect: true,
    fireDetect: true,
    petDetect: true,
    soundDetect: true,
  });

  return (
    <StorageContext.Provider
      value={{
        threshold,
        setThreshold,
        detectEvents,
        setDetectEvents,
      }}
    >
      <div className={styles.container}>
        {props.children}
        <Route exact path={path}>
          <NavigatorBar backIcon={isDarkMode ? arrowLeftDark : arrowLeft} />
          <div className={styles.title}>存储管理</div>
          <List className={styles.cameraList}>
            {isCameraInfoLoaded && cameras.map((camera) => {
              return(
              <List.Item
                key={camera.did}
                prefix={
                  <div className={styles.cameraImg}>
                    <PreloadImage
                      src={getCameraIcon(camera.model)}
                      style={{
                        width: 48,
                        height: 48,
                        objectFit: 'contain'
                      }}
                    />
                  </div>
                }
                title={camera.name}
                arrowIcon
                onClick={() => {
                  history.push({ pathname: `${path}/detail/${camera.did}`, state: { camera } });
                }}
              />
              )
          })}
          </List>
        </Route>
      </div>
    </StorageContext.Provider>
  );
};

export default StorageManagement;
