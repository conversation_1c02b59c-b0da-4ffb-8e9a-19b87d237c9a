import { PreloadImage } from '@/components/Image';
import styles from './index.module.scss';

import mac_show_img from '@/Resources/macBackup/mac_show_pic.png';
import nas_show_img from '@/Resources/macBackup/nas_show_pic.png';
import next from "@/Resources/modal/next.png";
import next_dark from "@/Resources/modal/next_dark.png";
import file_dir_icon from '@/Resources/layout/library2.png';

import List, { IListData, modalShow } from '@/components/List';
import { useCallback, useMemo, useRef, useState } from 'react';
import { Form, Input, Switch } from 'antd';
import { IMacBackupConfig, ListCard } from '../MacBackup_APP';
import { useTheme } from '@/utils/themeDetector';
import StorageInput from './storageInput';
import StorageLocation from './storageLocation';
import Modal from '@/components/Modal';
import { Toast } from '@/components/Toast/manager';
import { WebDavInfo } from '@/utils/DeviceType';

const MacBackup_Pc_Index_Page = () => {

  const { isDarkMode } = useTheme();
  const [form] = Form.useForm();
  const [createDirForm] = Form.useForm();
  const [openStorageLocationModal, setOpenStorageLocationModal] = useState<boolean>(false);
  const storageLocationRef = useRef<{ refresh: () => void }>(null);
  const [agreementIsOpen, setAgreementIsOpen] = useState<boolean>(false); // 确认是否已开启协议

  // 是否选择了用户信息
  const [isSelectedUser, setIsSelectedUser] = useState<boolean>(false);

  // 共享用户list
  const [sharedUserList, setSharedUserList] = useState<any[]>([
    { needPwd: true, userName: '共享账号1' },
    { needPwd: false, userName: '共享账号2' },
  ]);

  // 当前选择的用户信息
  const [selectedUser, setSelectedUser] = useState<any | undefined>();

  // 用户验证表单
  const [userForm] = Form.useForm();

  // 当前选择的文件夹路径
  const [selectedValue, setSelectedValue] = useState<string>('');

  // 当前所在目录路径
  const [currentDirPath, setCurrentDirPath] = useState<string>('');

  // webDav配置
  const [currentWebDav, setCurrentWebDav] = useState<WebDavInfo>(
    {
      alias_root: "/home/<USER>",
      password: "password",
      port: 5000,
      uri: "/",
      username: "user0"
    }
  );

  // mac备份配置
  const [macBackupConfig, setMacBackupConfig] = useState<IMacBackupConfig>({
    isBackup: false,
    path: '',
    sizeLimit: 0,
    unit: 'GB'
  });

  // 打开储存位置弹窗并关闭上一级弹窗
  const openEdit = useCallback((m) => {
    m.destroy();
    setOpenStorageLocationModal(true);
  }, [])

  // 打开存储设置弹窗
  const openStorageModal = useCallback(() => {
    const m = modalShow('存储设置', <StorageInput form={form} openEdit={() => openEdit(m)} />, async (m) => {
      return form.validateFields().then(value => {
        setMacBackupConfig(prev => ({ ...prev, path: value.location, sizeLimit: value.size ? value.size : 0, unit: value.unit ? value.unit : 'GB' }));
        m.destroy();
      }).catch((e) => console.log(e));
    }, () => null, false, { position: 'center', okBtnText: '保存' });
  }, [form, openEdit])

  // 提示位置需要设置
  const tipsSetBackupDir = useCallback((checked: boolean) => {
    modalShow('请设置存储位置', <span>若要开启Mac备份助手，您必须设置Mac备份内容的目标位置才能继续使用</span>, (m) => {
      m.destroy();
      setOpenStorageLocationModal(true);
      setMacBackupConfig(prev => ({ ...prev, isBackup: checked }));
      console.log('成功开启，备份助手功能开启')
    }, () => null, false, { position: 'center' });
  }, [])

  const enterLogin = useCallback((modal, checked, item) => {
    setIsSelectedUser(true);
    setSelectedUser(item);
    modal.destroy(); // 关闭上级弹窗

    if (item.needPwd) {
      modalShow(item.userName, <div className={styles.user_info_form_container}>
        <Form form={userForm}>
          <Form.Item name={'username'} rules={[{ required: true, message: '请输入用户名' }]}>
            <Input placeholder='请输入用户名' autoComplete='off' />
          </Form.Item>
          <Form.Item name={'password'} rules={[{ required: true, message: '请输入密码' }]}>
            <Input.Password placeholder='请输入密码' autoComplete='off' />
          </Form.Item>
        </Form>
      </div>, (m) => {
        userForm.validateFields().then(v => {
          m.destroy();
          setMacBackupConfig(prev => ({ ...prev, isBackup: checked })); // 开启备份助手功能
          tipsSetBackupDir(checked);
        })
      }, () => {
        setIsSelectedUser(false);
        setSelectedUser(undefined);
      }, false, { position: 'center' });
    } else {
      setMacBackupConfig(prev => ({ ...prev, isBackup: checked })); // 开启备份助手功能
      tipsSetBackupDir(checked);
    }
  }, [tipsSetBackupDir, userForm])

  const selectSharedUser = useCallback((checked: boolean) => {
    if (!isSelectedUser) {
      const m = modalShow('选择共享账号', <>
        {
          (sharedUserList.map((it, i) => (
            <ListCard key={`${it.userName}_${i}`} text={it.userName} subtext={it.needPwd ? '账号密码访问' : '无需密码访问'}
              onCallback={() => enterLogin(m, checked, it)} />
          )))
        }
      </>, () => null, () => null, true, { position: 'center' })
    }
  }, [enterLogin, isSelectedUser, sharedUserList])

  const isBackupOnChange = useCallback((checked: boolean) => {
    if (!checked) {
      // 取消
      setMacBackupConfig(prev => ({ ...prev, isBackup: checked }));
      return;
    }

    if (!agreementIsOpen) {
      modalShow('开启SMB/AFP网络共享服务', <span>若要Time Machine功能正确发现局域网内的智能存储设备，需要开启SMB/AFP网络共享服务。确定开启？</span>, (m) => {
        // 确定开启 todo
        console.log('确认开启该服务协议')
        setAgreementIsOpen(true);
        m.destroy();
        selectSharedUser(checked);
      }, () => null, false, { position: 'center' });
      return;
    }

    if (agreementIsOpen) selectSharedUser(checked);
  }, [agreementIsOpen, selectSharedUser])

  const optList: IListData[] = useMemo(() => {
    return [
      {
        key: 'open_mac_backup', type: 'switch', label: '开启Mac备份助手功能', render: () => {
          return <Switch value={macBackupConfig.isBackup} onChange={isBackupOnChange} />
        }
      },
      {
        key: 'back_path', type: 'text', 'label': '存储位置', render: () => {
          return <span className={`${styles.list_right_container} ${macBackupConfig.isBackup === false ? styles.list_right_container_disabled : ''}`} onClick={() => setOpenStorageLocationModal(true)}>
            <span className={styles.list_right_container_span}>{`${macBackupConfig.path === '' ? '未设置' : macBackupConfig.path}`}</span>
            <PreloadImage className={styles.list_right_container_img} src={isDarkMode ? next_dark : next} alt="next" />
          </span>
        }
      },
      {
        key: 'back_size_limit', type: 'input', 'label': '容量限制', render: () => {
          return <span className={`${styles.list_right_container} ${macBackupConfig.isBackup === false ? styles.list_right_container_disabled : ''}`} onClick={() => setOpenStorageLocationModal(true)}>
            <span className={styles.list_right_container_span}>{`${macBackupConfig.sizeLimit === 0 ? '未设置' : `${macBackupConfig.sizeLimit}${macBackupConfig.unit}`}`}</span>
            <PreloadImage className={styles.list_right_container_img} src={isDarkMode ? next_dark : next} alt="next" />
          </span>
        }
      }
    ]
  }, [isBackupOnChange, isDarkMode, macBackupConfig.isBackup, macBackupConfig.path, macBackupConfig.sizeLimit, macBackupConfig.unit]);

  // 创建文件夹
  const createDir = useCallback(() => {
    if (currentDirPath === '') {
      // 没有路径，则提示用户选择文件夹
      Toast.show('当前目录无法创建文件夹作为目录');
      return;
    }

    modalShow('新建文件夹', (
      <Form form={createDirForm}>
        <Form.Item name={'dir_name'}>
          <Input autoComplete='off' allowClear />
        </Form.Item>
      </Form>
    ), (m) => {
      createDirForm.validateFields().then(async (value) => {
        let dir_name = ''; // 文件夹名称
        if (selectedValue === '') {
          dir_name = currentDirPath.replace(currentWebDav.alias_root, '');
        } else {
          dir_name = selectedValue.replace(currentWebDav.alias_root, '');
        }
        const path = `${dir_name}${encodeURIComponent(value.dir_name)}`;

        // const hostWithoutPort = window.location.host.replace(/:\d+$/, '');
        const hostWithoutPort = '**************';
        const res = await fetch(`https://${hostWithoutPort}:${currentWebDav.port}${path}`, {
          method: 'MKCOL',
          headers: {
            'Depth': '1',
            'Content-Type': 'application/xml',
            'Authorization': 'Basic ' + btoa(`${currentWebDav.username}:${currentWebDav.password}`),
            'Range': 'bytes=0-1',
          }
        }).catch((e) => console.log('新建文件夹失败,', e));

        if (res && res.ok && res.status === 201) {
          m.destroy();
          if (storageLocationRef.current) storageLocationRef.current.refresh();
        }
      })
    }, () => createDirForm.resetFields(), false, { okBtnText: '确定', cancelBtnText: '取消', zIndex: 2002, position: 'center' });
  }, [createDirForm, currentDirPath, currentWebDav.alias_root, currentWebDav.password, currentWebDav.port, currentWebDav.username, selectedValue])

  // 存储路径
  const save_dir_path = useCallback(() => {
    const save = (path: string) => {
      console.log('当前路径为：', path);
      form.setFieldValue('location', path);
      setOpenStorageLocationModal(false);
      openStorageModal(); // 打开存储设置弹窗
    }

    if (selectedValue === '') {
      // 没有选择文件夹，则存储当前路径
      if (currentDirPath === '') {
        // 没有路径，则提示用户选择文件夹作为目录
        Toast.show('请选择一个文件夹作为目录');
        return;
      }

      save(currentDirPath);
      setCurrentDirPath(''); // 重置当前路径为空
      return;
    }
    save(selectedValue);
  }, [currentDirPath, form, openStorageModal, selectedValue])

  // 底部按钮容器
  const modalFooter = useMemo(() => {
    return <div className={styles.modal_footer}>
      <div className={`${styles.modal_footer_btns} ${styles.modal_footer_btns_other}`} onClick={createDir}>
        <PreloadImage src={file_dir_icon} />
        <span>新建文件夹</span>
      </div>
      <div className={styles.modal_footer_btns} onClick={save_dir_path}>
        <span>存储到当前路径</span>
      </div>
    </div>
  }, [createDir, save_dir_path])

  return (
    <div className={styles.root_container}>
      <div className={styles.header}>
        <div className={styles.header_img_show}>
          <PreloadImage src={mac_show_img} alt='mac' />
          <PreloadImage src={nas_show_img} alt='nas' />
          <PreloadImage src={mac_show_img} alt='mac' />
          <PreloadImage src={nas_show_img} alt='nas' />
        </div>
      </div>
      <div className={styles.content}>
        <span className={styles.content_span}>
          开启Mac备份助手功能后,可通过Mac电脑-Time Machine功能,发现本机指定的磁盘路径,并将其设置为外部备份磁盘。
        </span>

        <div className={styles.list_container}>
          <List dataSource={optList} />
        </div>
      </div>


      <Modal zIndex={2001} title='存储位置' isShow={openStorageLocationModal} onCancel={() => { setOpenStorageLocationModal(false); }}
        content={<StorageLocation ref={storageLocationRef} setSelectedValue={setSelectedValue} openStorageLocationModal={openStorageLocationModal}
          setCurrentDirPath={setCurrentDirPath} onChange={setCurrentWebDav} defaultValue={{
            alias_root: "/home/<USER>",
            password: "password",
            port: 5000,
            uri: "/",
            username: "user0"
          }} />} footer={modalFooter} />
    </div>
  )
}

export default MacBackup_Pc_Index_Page;